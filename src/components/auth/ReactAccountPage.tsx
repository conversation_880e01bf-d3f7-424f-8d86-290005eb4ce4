import React, { useEffect, useState, useRef } from "react";
import { useUser } from "../../contexts/UserContext";
import {
  fetchCustomerBookings,
  downloadBookingInvoice,
} from "../../utils/store/bookings";
import type { Booking as BaseBooking } from "../../utils/store/bookings";
import AccountLayout from "../account/AccountLayout";
import AccountDashboard from "../account/AccountDashboard";
import MyTripsTable from "../account/MyTripsTable";
import { dialCodes } from "../../constants";

// Extended Booking interface with all the properties we need
interface Booking extends Omit<BaseBooking, "status"> {
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  payment_status?: string;
  check_in_time?: string;
  check_out_time?: string;
  number_of_guests?: number;
  updated_at?: string;
  room_config_name?: string;
  hotel_id?: string;
  status: string; // Allow any string for status
}

interface UserData {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  country_code?: string;
  company_name?: string | null;
  created_at?: string;
}

// Define the shape of our user context
interface UserContext {
  user: UserData | null;
  isAuthenticated: boolean;
  loading: boolean;
  logout: () => Promise<void>;
}

// Pagination interface
interface PaginationState {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
}

const ReactAccountPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState<boolean>(true);
  const [showEditProfile, setShowEditProfile] = useState<boolean>(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showBookingModal, setShowBookingModal] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<string>("dashboard");
  const [downloadingInvoice, setDownloadingInvoice] = useState<boolean>(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const [profileUpdateLoading, setProfileUpdateLoading] = useState<boolean>(false);
  const [profileUpdateError, setProfileUpdateError] = useState<string | null>(null);
  const [profileUpdateSuccess, setProfileUpdateSuccess] = useState<boolean>(false);

  // Dial code selector state
  const [selectedDialCode, setSelectedDialCode] = useState("+41");
  const [isDialCodeDropdownOpen, setIsDialCodeDropdownOpen] = useState(false);
  const [dialCodeSearch, setDialCodeSearch] = useState("");
  const dialCodeDropdownRef = useRef<HTMLDivElement>(null);

  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 6,
  });

  // Get user context outside of useEffect
  let userContext: UserContext | null = null;
  try {
    userContext = useUser();
  } catch (error) {
    // Context not available, will use token-based auth
  }

  // Initialize dial code from user data
  useEffect(() => {
    if (userData) {
      // First try to use stored country_code, then parse from phone number
      if (userData.country_code) {
        setSelectedDialCode(userData.country_code);
      } else if (userData.phone) {
        const { countryCode } = parsePhoneNumber(userData.phone);
        setSelectedDialCode(countryCode);
      }
    }
  }, [userData]);

  // Close dial code dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dialCodeDropdownRef.current &&
        !dialCodeDropdownRef.current.contains(event.target as Node)
      ) {
        setIsDialCodeDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Authentication setup
  useEffect(() => {
    const initAuth = async () => {
      try {
        // First try to use UserContext if available
        if (userContext && userContext.user) {
          setUserData(userContext.user);
          setIsAuthenticated(true);
          setLoading(false);
          return;
        }

        // Fallback to token-based auth
        const token = localStorage.getItem("auth_token");
        if (token) {
          await fetchUserData(token);
        } else {
          redirectToLogin();
        }
      } catch (error) {
        setError("Authentication failed");
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Load all bookings data (no pagination on API side)
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setBookingsLoading(true);

        // Fetch all bookings from API (no limit/offset)
        const result = await fetchCustomerBookings();

        // Calculate total pages for frontend pagination
        const totalPages = Math.ceil(result.count / pagination.itemsPerPage);
        setPagination((prev) => ({
          ...prev,
          totalPages: totalPages || 1, // Ensure at least 1 page
        }));

        // Set all bookings data
        if (Array.isArray(result.bookings)) {
          setBookings(result.bookings);
        } else {
          console.error("Unexpected bookings data format:", result);
          setBookings([]);
        }
      } catch (error) {
        console.error("Error fetching bookings:", error);
        // Fallback to empty array if API fails
        setBookings([]);
        setPagination((prev) => ({
          ...prev,
          totalPages: 1,
        }));
      } finally {
        setBookingsLoading(false);
      }
    };

    if (isAuthenticated) {
      // Check if we have an auth token before trying to fetch bookings
      const authToken = localStorage.getItem("auth_token");
      if (authToken) {
        fetchBookings();
      } else {
        console.warn("No auth token found, skipping bookings fetch");
        setBookingsLoading(false);
      }
    }
  }, [isAuthenticated, pagination.itemsPerPage]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setPagination((prev) => ({
        ...prev,
        currentPage: newPage,
      }));
    }
  };

  // Fetch user data from API
  const fetchUserData = async (token: string) => {
    try {
      setLoading(true);
      const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
      const response = await fetch(
        `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": apiKey,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user data");
      }

      const data = await response.json();
      if (data.customer) {
        setUserData(data.customer);
        setIsAuthenticated(true);
      } else {
        redirectToLogin();
      }
    } catch (error) {
      setError("Failed to load account information");
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      if (userContext && userContext.logout) {
        await userContext.logout();
      } else {
        // Fallback to manual token removal if context not available
        localStorage.removeItem("auth_token");
      }
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      // Still redirect even if there's an error
      window.location.href = "/";
    }
  };

  // Redirect to login page
  const redirectToLogin = () => {
    setError("Please log in to access your account");
    setLoading(false);
    setTimeout(() => {
      window.location.href = "/login";
    }, 1500);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="text-center">
          <div className="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-red-50 border border-red-100 text-red-700 p-4 rounded-lg max-w-md">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {error}
          </div>
        </div>
      </div>
    );
  }

  // Not authenticated state
  if (!isAuthenticated || !userData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  // Filter dial codes based on search
  const filteredDialCodes = dialCodes.filter(
    (country) =>
      country.country.toLowerCase().includes(dialCodeSearch.toLowerCase()) ||
      country.code.includes(dialCodeSearch)
  );

  // Handle dial code selection
  const handleDialCodeSelect = (code: string) => {
    setSelectedDialCode(code);
    setIsDialCodeDropdownOpen(false);
    setDialCodeSearch("");
  };

  // Helper function to parse phone number and extract country code
  const parsePhoneNumber = (fullPhone: string) => {
    if (!fullPhone) return { countryCode: '+41', phoneNumber: '' };

    // Find the first space or the first non-digit after the + sign
    const match = fullPhone.match(/^(\+\d+)\s*(.*)$/);
    if (match) {
      return {
        countryCode: match[1],
        phoneNumber: match[2].trim()
      };
    }

    // If no country code found, assume it's just the phone number
    return {
      countryCode: '+41', // Default
      phoneNumber: fullPhone
    };
  };

  // Helper function to get display phone number
  const getDisplayPhoneNumber = () => {
    if (!userData?.phone) return "Not provided";

    const { countryCode, phoneNumber } = parsePhoneNumber(userData.phone);
    if (phoneNumber) {
      return `${countryCode} ${phoneNumber}`;
    }
    return userData.phone;
  };

  // Helper function to get phone number without country code for form
  const getPhoneNumberForForm = () => {
    if (!userData?.phone) return "";

    const { phoneNumber } = parsePhoneNumber(userData.phone);
    return phoneNumber;
  };

  // Handle profile update
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileUpdateLoading(true);
    setProfileUpdateError(null);
    setProfileUpdateSuccess(false);

    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const phoneNumber = formData.get('phone') as string;

      // Combine country code with phone number
      const fullPhoneNumber = phoneNumber ? `${selectedDialCode} ${phoneNumber}` : '';

      const updateData = {
        first_name: formData.get('first_name') as string,
        last_name: formData.get('last_name') as string,
        phone: fullPhoneNumber,
      };

      // Get auth token and API key
      const token = localStorage.getItem("auth_token");
      const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";

      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await fetch(
        `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
        {
          method: 'POST',
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": apiKey,
          },
          credentials: "include",
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update profile");
      }

      const data = await response.json();

      // Update local user data with the response
      if (data.customer) {
        setUserData(data.customer);
        setProfileUpdateSuccess(true);
        setShowEditProfile(false);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setProfileUpdateSuccess(false);
        }, 3000);
      }
    } catch (error: any) {
      console.error("Profile update error:", error);
      setProfileUpdateError(error.message || "Failed to update profile");
    } finally {
      setProfileUpdateLoading(false);
    }
  };

  // Handle opening booking details modal
  const openBookingModal = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingModal(true);
  };

  // Handle closing booking details modal
  const closeBookingModal = () => {
    setShowBookingModal(false);
    setSelectedBooking(null);
    setDownloadError(null);
  };

  // Handle download invoice
  const handleDownloadInvoice = async () => {
    if (!selectedBooking?.id) {
      setDownloadError("Booking ID not found");
      return;
    }

    setDownloadingInvoice(true);
    setDownloadError(null);

    try {
      await downloadBookingInvoice(selectedBooking.id);
    } catch (error: any) {
      console.error("Error downloading invoice:", error);
      setDownloadError(error.message || "Failed to download invoice");
    } finally {
      setDownloadingInvoice(false);
    }
  };

  // Handle view hotel
  const handleViewHotel = () => {
    if (selectedBooking?.hotel_id) {
      window.open(`/stays/${selectedBooking.hotel_id}`, "_blank");
    } else {
      // Fallback: try to construct URL from hotel name
      const hotelSlug = selectedBooking?.hotel_name
        ?.toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "");
      if (hotelSlug) {
        window.open(`/stays/${hotelSlug}`, "_blank");
      }
    }
  };

  // SVG icons
  const Icons = {
    user: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
    ),
    close: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    ),
    email: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
        <polyline points="22,6 12,13 2,6"></polyline>
      </svg>
    ),
    phone: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
      </svg>
    ),
    password: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
      </svg>
    ),
    signOut: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16 17 21 12 16 7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
    ),
    calendar: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="16" y1="2" x2="16" y2="6"></line>
        <line x1="8" y1="2" x2="8" y2="6"></line>
        <line x1="3" y1="10" x2="21" y2="10"></line>
      </svg>
    ),
    edit: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
      </svg>
    ),
    chevronLeft: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
    ),
    chevronRight: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>
    ),
  };

  // Format date for display in a more user-friendly way
  const formatDisplayDate = (dateString: string) => {
    const date = new Date(dateString);

    // Get day of week, day, month and year
    const dayOfWeek = date.toLocaleDateString("en-US", { weekday: "long" });
    const day = date.getDate();
    const month = date.toLocaleDateString("en-US", { month: "long" });
    const year = date.getFullYear();

    // Add ordinal suffix to day (1st, 2nd, 3rd, etc.)
    const ordinalSuffix = (day: number): string => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    // Return formatted date: "Monday, 15th January 2024"
    return `${dayOfWeek}, ${day}${ordinalSuffix(day)} ${month} ${year}`;
  };

  // Format date and time for display in a more user-friendly way
  const formatDisplayDateTime = (dateString: string) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid date";

    // Get the date part
    const formattedDate = formatDisplayDate(dateString);

    // Get the time part
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    // Return formatted date and time: "Monday, 15th January 2024 at 2:30 PM"
    return `${formattedDate} at ${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  // Edit Profile Form Component
  const EditProfileForm = () => (
    <form onSubmit={handleProfileUpdate} className="space-y-4">
      {/* Error Message */}
      {profileUpdateError && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-red-700 text-sm">{profileUpdateError}</span>
          </div>
        </div>
      )}

      <div>
        <label
          htmlFor="first_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          First Name
        </label>
        <input
          type="text"
          id="first_name"
          name="first_name"
          defaultValue={userData?.first_name || ""}
          disabled={profileUpdateLoading}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          required
        />
      </div>

      <div>
        <label
          htmlFor="last_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Last Name
        </label>
        <input
          type="text"
          id="last_name"
          name="last_name"
          defaultValue={userData?.last_name || ""}
          disabled={profileUpdateLoading}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          required
        />
      </div>

      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          defaultValue={userData?.email || ""}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors bg-gray-50"
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
      </div>

      <div>
        <label
          htmlFor="phone"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number
        </label>
        <div className="flex">
          <div className="relative w-20" ref={dialCodeDropdownRef}>
            <button
              type="button"
              onClick={() =>
                setIsDialCodeDropdownOpen(!isDialCodeDropdownOpen)
              }
              disabled={profileUpdateLoading}
              className="w-full h-full border border-gray-200 rounded-l-xl px-2 py-2 focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6] bg-gray-50 text-sm flex items-center justify-between disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{selectedDialCode}</span>
              <svg
                className={`w-4 h-4 transition-transform ${
                  isDialCodeDropdownOpen ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {isDialCodeDropdownOpen && (
              <div className="absolute top-full left-0 w-80 z-50 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-hidden">
                <div className="p-2 border-b border-gray-200">
                  <input
                    type="text"
                    placeholder="Search country or code..."
                    value={dialCodeSearch}
                    onChange={(e) => setDialCodeSearch(e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6]"
                    autoFocus
                  />
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {filteredDialCodes.length > 0 ? (
                    filteredDialCodes.map((country, index) => (
                      <button
                        key={`${country.code}-${index}`}
                        type="button"
                        onClick={() => handleDialCodeSelect(country.code)}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center justify-between ${
                          selectedDialCode === country.code
                            ? "bg-blue-50 text-[#285DA6]"
                            : ""
                        }`}
                      >
                        <span className="truncate">{country.country}</span>
                        <span className="text-gray-500 ml-2 flex-shrink-0">
                          {country.code}
                        </span>
                      </button>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-sm text-gray-500">
                      No countries found
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <input
            type="tel"
            id="phone"
            name="phone"
            defaultValue={getPhoneNumberForForm()}
            disabled={profileUpdateLoading}
            placeholder="Phone Number"
            className="flex-1 border border-gray-200 border-l-0 rounded-r-xl px-3 py-2 focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={() => {
            setShowEditProfile(false);
            setProfileUpdateError(null);
            setIsDialCodeDropdownOpen(false);
            setDialCodeSearch("");
          }}
          disabled={profileUpdateLoading}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={profileUpdateLoading}
          className="px-4 py-2 bg-[#285DA6] text-white rounded-md hover:bg-[#1A3A6E] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {profileUpdateLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </form>
  );

  // Booking Modal Component
  const BookingModal = () => {
    if (!selectedBooking || !showBookingModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[300] p-4 backdrop-blur-sm animate-fadeIn">
        <div
          className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-scaleIn"
          style={{
            boxShadow:
              "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
          }}
        >
          {/* Header - Modernized */}
          <div className="relative bg-gradient-to-r from-[#285DA6] to-[#1A3A6E] rounded-t-2xl">
            <div className="flex justify-between items-center p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white"
                  >
                    <rect
                      x="3"
                      y="4"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white">
                    Booking Details
                  </h3>
                  {/* <p className="text-white/80 text-sm">
                    Reference ID: {selectedBooking.id}
                  </p> */}
                </div>
              </div>
              <button
                onClick={closeBookingModal}
                className="text-white/80 hover:text-white transition-colors rounded-xl p-2 hover:bg-white/10"
                aria-label="Close modal"
              >
                {Icons.close}
              </button>
            </div>
          </div>

          {/* Hotel banner - Modernized */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-2xl font-semibold text-gray-900 mb-2">
                      {selectedBooking.hotel_name}
                    </h4>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium bg-[#285DA6]/10 text-[#285DA6]">
                        {selectedBooking.room_config_name ||
                          selectedBooking.room_type}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-[#285DA6] mb-1">
                      {selectedBooking.currency_code.toUpperCase()}{" "}
                      {selectedBooking.total_amount.toFixed(2)}
                    </div>
                    <p className="text-sm text-gray-500">Total Amount</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    onClick={handleDownloadInvoice}
                    disabled={downloadingInvoice}
                    className="flex items-center justify-center px-6 py-3 bg-[#285DA6] text-white rounded-xl hover:bg-[#1A3A6E] hover:shadow-md transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {downloadingInvoice ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Downloading...
                      </>
                    ) : (
                      <>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Download Invoice
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleViewHotel}
                    className="flex items-center justify-center px-6 py-3 border border-[#285DA6] text-[#285DA6] rounded-xl hover:bg-[#285DA6] hover:text-white hover:shadow-md transition-all duration-200 font-medium"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                      <polyline points="15 3 21 3 21 9"></polyline>
                      <line x1="10" y1="14" x2="21" y2="3"></line>
                    </svg>
                    View Hotel
                  </button>
                </div>

                {/* Download Error */}
                {downloadError && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl">
                    <p className="text-sm text-red-600">{downloadError}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Dates section - Modernized */}
            <div className="bg-gradient-to-r from-[#285DA6]/5 to-[#285DA6]/10 rounded-2xl overflow-hidden mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-[#285DA6]/20">
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                        <circle cx="8" cy="14" r="2"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-[#285DA6] uppercase tracking-wider font-medium mb-1">
                        Check-in
                      </p>
                      <p className="text-lg font-medium text-gray-800">
                        {formatDisplayDate(selectedBooking.check_in_date)}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center mt-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        After {selectedBooking.check_in_time || "14:00"}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                        <circle cx="16" cy="16" r="2"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-[#285DA6] uppercase tracking-wider font-medium mb-1">
                        Check-out
                      </p>
                      <p className="text-lg font-medium text-gray-800">
                        {formatDisplayDate(selectedBooking.check_out_date)}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center mt-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        Before {selectedBooking.check_out_time || "11:00"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Guest Info - Modernized */}
            <div className="mb-8">
              <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                Guest Information
              </h5>
              <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Guest Name
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_name || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Email
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_email || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Phone
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_phone || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Number of Guests
                      </p>
                      <p className="font-medium">
                        {selectedBooking.number_of_guests || 1}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Booking Information - Modernized */}
            <div className="bg-gray-50/50 p-6 rounded-2xl border border-gray-100">
              <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                  </svg>
                </div>
                Booking Information
              </h5>
              <div className="space-y-2 text-sm">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-500">Created</span>
                  <span className="font-medium text-gray-800">
                    {formatDisplayDateTime(selectedBooking.created_at)}
                  </span>
                </div>
                {selectedBooking.updated_at && (
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2">
                    <span className="text-gray-500">Last Updated</span>
                    <span className="font-medium text-gray-800">
                      {formatDisplayDateTime(selectedBooking.updated_at)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render content based on active section
  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        // Calculate upcoming trips (check-in date is in the future)
        const upcomingTripsCount = bookings.filter(
          (booking) => new Date(booking.check_in_date) > new Date()
        ).length;

        return (
          <AccountDashboard
            userData={userData!}
            bookingsCount={bookings.length}
            upcomingTrips={upcomingTripsCount}
            onEditProfile={() => setActiveSection("profile")}
            onViewTrips={() => setActiveSection("trips")}
          />
        );
      case "trips":
        // Calculate pagination for frontend
        const startIndex =
          (pagination.currentPage - 1) * pagination.itemsPerPage;
        const endIndex = startIndex + pagination.itemsPerPage;
        const paginatedBookings = bookings.slice(startIndex, endIndex);

        return (
          <MyTripsTable
            bookings={paginatedBookings}
            loading={bookingsLoading}
            onBookingClick={openBookingModal}
            pagination={pagination}
            onPageChange={handlePageChange}
          />
        );
      case "profile":
        return (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">
              Profile Settings
            </h2>

            {/* Success Message */}
            {profileUpdateSuccess && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                <div className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-green-500"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-green-700 text-sm">Profile updated successfully!</span>
                </div>
              </div>
            )}

            {showEditProfile ? (
              <EditProfileForm />
            ) : (
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                    <div className="md:w-1/3">
                      <label className="text-sm font-medium text-gray-500">
                        First Name
                      </label>
                    </div>
                    <div className="md:w-2/3">
                      <p className="text-gray-900 font-medium">
                        {userData?.first_name || "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                    <div className="md:w-1/3">
                      <label className="text-sm font-medium text-gray-500">
                        Last Name
                      </label>
                    </div>
                    <div className="md:w-2/3">
                      <p className="text-gray-900 font-medium">
                        {userData?.last_name || "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                    <div className="md:w-1/3">
                      <label className="text-sm font-medium text-gray-500">
                        Email Address
                      </label>
                    </div>
                    <div className="md:w-2/3">
                      <p className="text-gray-900 font-medium">
                        {userData?.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row md:items-center py-3">
                    <div className="md:w-1/3">
                      <label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </label>
                    </div>
                    <div className="md:w-2/3">
                      <p className="text-gray-900 font-medium">
                        {getDisplayPhoneNumber()}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="pt-6 border-t border-gray-100">
                  <button
                    onClick={() => {
                      setShowEditProfile(true);
                      setProfileUpdateError(null);
                      setProfileUpdateSuccess(false);
                      setIsDialCodeDropdownOpen(false);
                      setDialCodeSearch("");
                    }}
                    className="px-4 py-2 bg-[#285DA6] text-white rounded-xl hover:bg-[#1A3A6E] hover:shadow-md transition-all duration-200 font-medium"
                  >
                    Edit Profile
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <AccountLayout
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      userName={userData?.first_name}
      userEmail={userData?.email}
      onLogout={handleLogout}
    >
      {/* Booking Details Modal */}
      <BookingModal />

      {/* Main Content */}
      <div className="section-transition h-[85vh]">{renderContent()}</div>
    </AccountLayout>
  );
};

export default ReactAccountPage;
